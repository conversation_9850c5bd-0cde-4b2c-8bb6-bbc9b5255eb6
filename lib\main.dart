import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:provider/provider.dart';
import 'widgets/auth_gate.dart';
import 'services/theme_notifier.dart';

/// Global stream subscription for authentication state changes
StreamSubscription<AuthState>? _authSubscription;

/// Main entry point of the SipTracker application
///
/// Initializes Supabase, sets up authentication state management, and runs the app.
/// Handles initialization errors gracefully to ensure the app works even without
/// proper Supabase configuration.
void main() async {
  // Ensure Flutter binding is initialized before calling async operations
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Supabase with configuration
    // SECURITY WARNING: In production, these credentials should be stored in environment variables
    // or secure configuration files, not hardcoded in the source code.
    // For development and testing purposes, we're using direct values here.
    await Supabase.initialize(
      url: 'https://tzpfjfcjumcxucynuqph.supabase.co',
      anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6cGZqZmNqdW1jeHVjeW51cXBoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwNjIxNjgsImV4cCI6MjA2NDYzODE2OH0.hYDbSs1ctJNDyy-8S-SEtMpcgzGD0JmO5rvcBtITm7I',
    );

    // Set up authentication state listener
    _setupAuthListener();

    // Run the app after successful initialization
    runApp(const SipTrackerApp());
  } catch (e) {
    // Handle Supabase initialization errors
    debugPrint('Failed to initialize Supabase: $e');

    // Update auth state to indicate no authentication available
    updateAuthState(null);

    // Run the app even if Supabase initialization fails
    // This allows the app to function in offline mode or with placeholder data
    runApp(const SipTrackerApp());
  }
}

/// Sets up the authentication state listener
///
/// This function creates a stream subscription to Supabase's authentication
/// state changes and updates the global authentication state accordingly.
/// It handles sign-in, sign-out, and token refresh events automatically.
void _setupAuthListener() {
  try {
    // Get the current user session on app start
    final currentSession = Supabase.instance.client.auth.currentSession;
    updateAuthState(currentSession?.user);

    // Listen to authentication state changes
    _authSubscription = Supabase.instance.client.auth.onAuthStateChange.listen(
      (AuthState authState) {
        debugPrint('Auth state changed: ${authState.event}');

        // Update global authentication state
        updateAuthState(authState.session?.user);
      },
      onError: (error) {
        debugPrint('Auth state listener error: $error');
        // On error, assume user is not authenticated
        updateAuthState(null);
      },
    );
  } catch (e) {
    debugPrint('Failed to set up auth listener: $e');
    // If we can't set up the listener, assume no authentication
    updateAuthState(null);
  }
}

/// Main application widget with authentication-aware routing
///
/// This widget sets up the MaterialApp with the AuthGate as the home widget,
/// which automatically handles routing based on authentication state.
class SipTrackerApp extends StatefulWidget {
  const SipTrackerApp({super.key});

  @override
  State<SipTrackerApp> createState() => _SipTrackerAppState();
}

class _SipTrackerAppState extends State<SipTrackerApp> {
  /// Builds the light theme with improved color contrast and readability
  ///
  /// This method creates a comprehensive light theme that ensures proper contrast
  /// ratios for all UI elements, especially addressing issues with coffee brown
  /// and other accent colors that may have poor readability.
  ThemeData _buildLightTheme(Color accentColor) {
    final baseColorScheme = ColorScheme.fromSeed(
      seedColor: accentColor,
      brightness: Brightness.light,
    );

    // Special handling for coffee brown to ensure readability
    final isSpecialCoffeeColor = accentColor.toARGB32() == 0xFF6F4E37;

    final colorScheme = isSpecialCoffeeColor
        ? baseColorScheme.copyWith(
            // Ensure coffee brown has proper contrast
            primary: const Color(0xFF6F4E37),
            onPrimary: Colors.white,
            primaryContainer: const Color(0xFFF5F5DC), // Cream color
            onPrimaryContainer: const Color(0xFF6F4E37),
            secondary: const Color(0xFF8B6F47),
            onSecondary: Colors.white,
            surface: Colors.white,
            onSurface: Colors.black87,
          )
        : baseColorScheme;

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,

      // Enhanced component themes for better readability
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          foregroundColor: colorScheme.onPrimary,
          backgroundColor: colorScheme.primary,
          disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
          disabledBackgroundColor: colorScheme.onSurface.withValues(alpha: 0.12),
        ),
      ),

      cardTheme: CardThemeData(
        color: colorScheme.surface,
        surfaceTintColor: colorScheme.surfaceTint,
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),

      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surface,
        selectedColor: colorScheme.primaryContainer,
        disabledColor: colorScheme.onSurface.withValues(alpha: 0.12),
        labelStyle: TextStyle(color: colorScheme.onSurface),
        secondaryLabelStyle: TextStyle(color: colorScheme.onPrimaryContainer),
        side: BorderSide(color: colorScheme.outline),
      ),

      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        surfaceTintColor: colorScheme.surfaceTint,
        elevation: 0,
      ),

      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        labelStyle: TextStyle(color: colorScheme.onSurfaceVariant),
        hintStyle: TextStyle(color: colorScheme.onSurfaceVariant),
      ),
    );
  }

  /// Builds the dark theme with improved color contrast and readability
  ///
  /// This method creates a comprehensive dark theme that maintains proper contrast
  /// ratios while preserving the coffee theme aesthetic in dark mode.
  ThemeData _buildDarkTheme(Color accentColor) {
    final baseColorScheme = ColorScheme.fromSeed(
      seedColor: accentColor,
      brightness: Brightness.dark,
    );

    // Special handling for coffee brown in dark mode
    final isSpecialCoffeeColor = accentColor.toARGB32() == 0xFF6F4E37;

    final colorScheme = isSpecialCoffeeColor
        ? baseColorScheme.copyWith(
            // Coffee brown adjustments for dark mode
            primary: const Color(0xFF8B6F47), // Lighter coffee brown for dark mode
            onPrimary: Colors.white,
            primaryContainer: const Color(0xFF6F4E37),
            onPrimaryContainer: const Color(0xFFF5F5DC),
            secondary: const Color(0xFFA67C52),
            onSecondary: Colors.black,
            surface: const Color(0xFF1C1B1F),
            onSurface: Colors.white,
          )
        : baseColorScheme;

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,

      // Enhanced component themes for dark mode readability
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          foregroundColor: colorScheme.onPrimary,
          backgroundColor: colorScheme.primary,
          disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
          disabledBackgroundColor: colorScheme.onSurface.withValues(alpha: 0.12),
        ),
      ),

      cardTheme: CardThemeData(
        color: colorScheme.surface,
        surfaceTintColor: colorScheme.surfaceTint,
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),

      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surface,
        selectedColor: colorScheme.primaryContainer,
        disabledColor: colorScheme.onSurface.withValues(alpha: 0.12),
        labelStyle: TextStyle(color: colorScheme.onSurface),
        secondaryLabelStyle: TextStyle(color: colorScheme.onPrimaryContainer),
        side: BorderSide(color: colorScheme.outline),
      ),

      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        surfaceTintColor: colorScheme.surfaceTint,
        elevation: 0,
      ),

      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        labelStyle: TextStyle(color: colorScheme.onSurfaceVariant),
        hintStyle: TextStyle(color: colorScheme.onSurfaceVariant),
      ),
    );
  }

  @override
  void dispose() {
    // Clean up the authentication state listener to prevent memory leaks
    _authSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ThemeNotifier(),
      child: Consumer<ThemeNotifier>(
        builder: (context, themeNotifier, child) {
          return MaterialApp(
            title: 'SipTracker',
            themeMode: themeNotifier.themeMode,
            theme: _buildLightTheme(themeNotifier.seedColor),
            darkTheme: _buildDarkTheme(themeNotifier.seedColor),
            home: const AuthGate(),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}
