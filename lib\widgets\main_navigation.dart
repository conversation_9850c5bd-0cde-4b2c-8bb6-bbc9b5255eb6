import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:water_drop_nav_bar/water_drop_nav_bar.dart';
import '../screens/home_screen.dart';
import '../screens/map_screen.dart';
import '../screens/favorites_screen.dart';
import '../screens/profile_screen.dart';
import '../services/theme_notifier.dart';

class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const MapScreen(),
    const FavoritesScreen(),
    const ProfileScreen(),
  ];

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  /// Creates the list of bar items for the WaterDropNavBar
  ///
  /// Each item includes both filled and outlined icons for better visual feedback
  /// when switching between selected and unselected states.
  List<BarItem> _buildBarItems() {
    return [
      BarItem(
        filledIcon: Icons.home,
        outlinedIcon: Icons.home_outlined,
      ),
      BarItem(
        filledIcon: Icons.map,
        outlinedIcon: Icons.map_outlined,
      ),
      BarItem(
        filledIcon: Icons.favorite,
        outlinedIcon: Icons.favorite_border,
      ),
      BarItem(
        filledIcon: Icons.person,
        outlinedIcon: Icons.person_outline,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeNotifier>(
      builder: (context, themeNotifier, child) {
        return Scaffold(
          body: IndexedStack(
            index: _currentIndex,
            children: _screens,
          ),
          bottomNavigationBar: WaterDropNavBar(
            selectedIndex: _currentIndex,
            onItemSelected: _onTabTapped,
            barItems: _buildBarItems(),
            // Theme integration with dynamic colors
            waterDropColor: themeNotifier.seedColor,
            backgroundColor: Theme.of(context).colorScheme.surface,
            inactiveIconColor: Theme.of(context).colorScheme.onSurfaceVariant,
            iconSize: 24.0,
            // Additional styling for better Material 3 integration
            bottomPadding: 8.0,
          ),
        );
      },
    );
  }
}
