import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Enum representing user-selectable theme mode options
/// 
/// This enum provides three options for theme selection:
/// - [light]: Always use light theme
/// - [dark]: Always use dark theme  
/// - [system]: Follow system theme preference
enum ThemeModeOption {
  light,
  dark,
  system,
}



/// Theme management service that handles user theme preferences
///
/// This service extends [ChangeNotifier] to provide reactive theme updates
/// throughout the app. It manages theme mode (light/dark/system) preferences,
/// persisting them using SharedPreferences. The accent color is fixed to
/// coffee brown to maintain consistent branding.
///
/// The service follows the established patterns in the app:
/// - Comprehensive error handling with graceful degradation
/// - Async initialization in constructor
/// - Debug logging for development
/// - Production-ready with proper state management
///
/// Example usage:
/// ```dart
/// final themeNotifier = ThemeNotifier();
///
/// // Get current theme mode
/// final currentMode = themeNotifier.currentThemeMode;
///
/// // Change theme mode
/// await themeNotifier.setThemeMode(ThemeModeOption.dark);
///
/// // Get fixed coffee brown seed color
/// final seedColor = themeNotifier.seedColor;
/// ```
class ThemeNotifier extends ChangeNotifier {
  // Private state variables
  ThemeModeOption _currentThemeMode = ThemeModeOption.system;

  // SharedPreferences keys for persistence
  static const String _themeModeKey = 'theme_mode';
  
  /// Constructor that initializes theme preferences from storage
  /// 
  /// Automatically loads saved preferences on instantiation.
  /// If loading fails, falls back to default values (system theme, first accent color).
  ThemeNotifier() {
    _loadPreferences();
  }
  
  /// Gets the current theme mode option selected by the user
  ThemeModeOption get currentThemeMode => _currentThemeMode;

  /// Gets the fixed coffee brown seed color for theme generation
  Color get seedColor => const Color(0xFF6F4E37);
  
  /// Converts the user's theme mode selection to Flutter's ThemeMode
  /// 
  /// This getter provides the conversion between our custom enum and
  /// Flutter's built-in ThemeMode enum for use with MaterialApp.
  ThemeMode get themeMode {
    switch (_currentThemeMode) {
      case ThemeModeOption.light:
        return ThemeMode.light;
      case ThemeModeOption.dark:
        return ThemeMode.dark;
      case ThemeModeOption.system:
        return ThemeMode.system;
    }
  }
  
  /// Loads theme preferences from SharedPreferences
  /// 
  /// This method handles async initialization and provides comprehensive
  /// error handling. If SharedPreferences fails or saved values are invalid,
  /// the app gracefully falls back to default values.
  /// 
  /// Default values:
  /// - Theme mode: [ThemeModeOption.system]
  /// - Accent color: First color in [accentColors] list
  Future<void> _loadPreferences() async {
    try {
      debugPrint('ThemeNotifier: Loading theme preferences...');
      
      final prefs = await SharedPreferences.getInstance();
      
      // Load theme mode preference
      final savedThemeModeIndex = prefs.getInt(_themeModeKey);
      if (savedThemeModeIndex != null && 
          savedThemeModeIndex >= 0 && 
          savedThemeModeIndex < ThemeModeOption.values.length) {
        _currentThemeMode = ThemeModeOption.values[savedThemeModeIndex];
        debugPrint('ThemeNotifier: Loaded theme mode: $_currentThemeMode');
      } else {
        debugPrint('ThemeNotifier: No valid theme mode found, using system default');
      }
      

      
      // Notify listeners after loading preferences
      notifyListeners();
      
    } catch (e) {
      debugPrint('ThemeNotifier: Failed to load preferences: $e');
      // Graceful degradation - continue with default values
      // No need to notify listeners as values haven't changed from defaults
    }
  }
  
  /// Updates the theme mode preference
  /// 
  /// [mode] The new theme mode to apply
  /// 
  /// This method updates both the in-memory state and persisted preference.
  /// If saving to SharedPreferences fails, the in-memory state is still updated
  /// to ensure the UI remains responsive.
  /// 
  /// Automatically notifies listeners to trigger UI updates throughout the app.
  Future<void> setThemeMode(ThemeModeOption mode) async {
    if (_currentThemeMode == mode) {
      return; // No change needed
    }
    
    debugPrint('ThemeNotifier: Setting theme mode to: $mode');
    
    // Update in-memory state first for immediate UI response
    _currentThemeMode = mode;
    notifyListeners();
    
    // Persist the preference
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeModeKey, mode.index);
      debugPrint('ThemeNotifier: Theme mode saved successfully');
    } catch (e) {
      debugPrint('ThemeNotifier: Failed to save theme mode preference: $e');
      // Continue execution - the UI will still work with in-memory state
    }
  }
  
}
